<template>
  <view class="container">
    <!-- 顶部渐变背景 -->
    <view class="header-bg"></view>
    
    <!-- 头部欢迎区 -->
    <view class="welcome-section">
      <view class="welcome-text">
        <text class="greeting">您好，尊敬的用户</text>
        <text class="sub-greeting">欢迎使用燃气服务平台</text>
      </view>
      <view class="user-avatar">
        <image src="/static/images/avatar.png" mode="aspectFill"></image>
      </view>
    </view>
    
    <!-- 租户列表 -->
    <view class="stores-container">
      <!-- 无租户时的缺省状态 -->
      <view class="empty-store-container" v-if="!storeList.length">
        <image src="/static/images/empty-store.png" mode="aspectFit" class="empty-store-image"></image>
        <view class="empty-store-text">暂无注册租户</view>
        <view class="empty-store-subtext">注册租户后，可以享受燃气服务</view>
        <button class="btn-primary register-btn" @click="handleRegisterStore">注册新租户</button>
      </view>

      <!-- 每个租户卡片 -->
      <view 
        v-else
        v-for="(store, index) in storeList" 
        :key="store.id" 
        class="store-card"
        :class="{'store-active': selectedStoreId === store.id}"
      >
        <!-- 租户基本信息 -->
        <view class="store-header">
          <!-- <image :src="store.image" mode="aspectFill" class="store-image"></image> -->
          <view class="store-info">
            <view class="store-name" @click.stop="viewStoreDetail(store)">{{store.name}}</view>
            <view class="store-address">{{store.address}}</view>
            <!-- <view class="store-tag" v-if="store.isRecommended">推荐</view> -->
          </view>
          <view class="store-map" @click.stop="showFullScreenMap(store)">
            <map
              :latitude="store.latitude"
              :longitude="store.longitude"
              :markers="[{
                id: store.id,
                latitude: store.latitude,
                longitude: store.longitude,
                title: store.name
              }]"
              scale="16"
              class="map"
            ></map>
          </view>
        </view>
        
        <!-- 已审核通过的租户展示运营数据 -->
        <view class="store-status" v-if="store.canOrder" @click.stop="viewStoreDetail(store)">
          <view class="status-item operational-item">
            <view class="item-icon icon-operational">
              <uni-icons type="tubiao" color="#4c6ef5" size="22"></uni-icons>
            </view>
            <view class="item-text">气瓶数量</view>
            <view class="operational-value">{{store.cylinderCount || 0}}个</view>
          </view>
          
          <view class="status-item operational-item">
            <view class="item-icon icon-operational">
              <uni-icons type="sound" color="#4c6ef5" size="22"></uni-icons>
            </view>
            <view class="item-text">报警器</view>
            <view class="operational-value">{{store.alarmCount || 0}}个</view>
          </view>
          
          <view class="status-item operational-item">
            <view class="item-icon" :class="{'icon-abnormal': store.hasAbnormal, 'icon-normal': !store.hasAbnormal}">
              <uni-icons :type="store.hasAbnormal ? 'error' : 'checkmarkempty'" 
                       :color="store.hasAbnormal ? '#fa3534' : '#19be6b'" size="22"></uni-icons>
            </view>
            <view class="item-text">异常状态</view>
            <view class="status-tag" :class="store.hasAbnormal ? 'tag-abnormal' : 'tag-normal'">
              {{ store.hasAbnormal ? '有异常' : '正常' }}
            </view>
          </view>
        </view>
        
        <!-- 未审核通过的租户展示审核状态 -->
        <view class="store-status" v-else @click.stop="viewStoreDetail(store)">

          <!-- <view class="status-item" :class="{ 'status-passed': store.insuranceStatus === 'passed' }">
            <view class="item-icon" :class="{ 'icon-passed': store.insuranceStatus === 'passed' }">
              <uni-icons :type="store.insuranceStatus === 'passed' ? 'checkbox-filled' : 'closeempty'" 
                       :color="store.insuranceStatus === 'passed' ? '#19be6b' : '#fa3534'" size="18"></uni-icons>
            </view>
            <view class="item-text">保险服务</view>
            <view class="status-tag" :class="store.insuranceStatus === 'passed' ? 'tag-passed' : 'tag-pending'">
              {{ store.insuranceStatus === 'passed' ? '已通过' : '待审核' }}
            </view>
          </view> -->
          
          <view class="status-item" :class="{ 'status-passed': store.safetyStatus === 'passed' }">
            <view class="item-icon" :class="{ 'icon-passed': store.safetyStatus === 'passed' }">
              <uni-icons :type="store.safetyStatus === 'passed' ? 'checkbox-filled' : 'closeempty'" 
                       :color="store.safetyStatus === 'passed' ? '#19be6b' : '#fa3534'" size="18"></uni-icons>
            </view>
            <view class="item-text">设备安装</view>
            <view class="status-tag" :class="store.safetyStatus === 'passed' ? 'tag-passed' : 'tag-pending'">
              {{ store.safetyStatus === 'passed' ? '已安装' : '未安装' }}
            </view>
          </view>
          
          <view class="status-item" :class="{
            'status-passed': store.safetyGuidanceStatus === 'passed',
            'status-failed': store.safetyGuidanceStatus === 'failed',
            'status-scheduled': store.safetyGuidanceStatus === 'scheduled'
          }">
            <view class="item-icon" :class="{
              'icon-passed': store.safetyGuidanceStatus === 'passed',
              'icon-failed': store.safetyGuidanceStatus === 'failed',
              'icon-scheduled': store.safetyGuidanceStatus === 'scheduled'
            }">
              <uni-icons
                :type="getSafetyGuidanceIcon(store.safetyGuidanceStatus)"
                :color="getSafetyGuidanceColor(store.safetyGuidanceStatus)"
                size="18">
              </uni-icons>
            </view>
            <view class="item-text">安全指导</view>
            <view class="status-tag" :class="getSafetyGuidanceTagClass(store.safetyGuidanceStatus)">
              {{ getSafetyGuidanceStatusText(store.safetyGuidanceStatus) }}
            </view>
          </view>

          <view class="status-item" :class="{ 'status-passed': store.contractStatus === 'signed' }">
            <view class="item-icon" :class="{ 'icon-passed': store.contractStatus === 'signed' }">
              <uni-icons :type="store.contractStatus === 'signed' ? 'checkbox-filled' : 'closeempty'"
                       :color="store.contractStatus === 'signed' ? '#19be6b' : '#fa3534'" size="18"></uni-icons>
            </view>
            <view class="item-text">电子合同</view>
            <view class="status-tag" :class="store.contractStatus === 'signed' ? 'tag-passed' : 'tag-pending'">
              {{ store.contractStatus === 'signed' ? '已签署' : '未签署' }}
            </view>
          </view>
        </view>
        
        <!-- 所有租户都显示审核状态 -->
        <view class="audit-status-row" v-if="store.canOrder">

          <view class="audit-item" :class="{ 'audit-passed': store.safetyStatus === 'passed' }">
            <!-- <view class="audit-icon" :class="{ 'icon-passed': store.safetyStatus === 'passed' }">
              <uni-icons :type="store.safetyStatus === 'passed' ? 'checkbox-filled' : 'closeempty'" 
                     :color="store.safetyStatus === 'passed' ? '#19be6b' : '#fa3534'" size="14"></uni-icons>
            </view> -->
            <view class="audit-text">设备安装: {{ store.safetyStatus === 'passed' ? '已安装' : '未安装' }}</view>
          </view>
        </view>
        
        <!-- 租户订气按钮 -->
        <view class="store-action">
          <view class="action-buttons" :style="{ 'justify-content': (store.canOrder || store.contractStatus === 'signed') ? 'space-between' : 'flex-end' }">
            <button
              class="btn-warning schedule-btn"
              v-if="!store.canOrder && hasBasicInfo(store) && !isWorkOrderCompleted(store) && !hasAppointment(store)"
              @click.stop="handleGoToAppointment(store)"
            >
              前往预约
            </button>
            <button
              class="btn-warning schedule-btn"
              v-if="!store.canOrder && hasBasicInfo(store) && !isWorkOrderCompleted(store) && hasAppointment(store)"
              @click.stop="handleViewAppointment(store)"
            >
              查看预约
            </button>
            <button
              class="btn-info contract-btn"
              v-if="!store.canOrder && hasBasicInfo(store) && isWorkOrderCompleted(store) && store.contractStatus !== 'signed'"
              @click.stop="handleSignContract(store)"
            >
              签署合同
            </button>
            <button
              class="btn-secondary edit-btn"
              v-if="!(store.contractStatus === 'signed' && hasAppointment(store))"
              @click.stop="handleEditStore(store)"
            >
              编辑租户
            </button>
            <button
              class="btn-primary order-btn"
              v-if="store.canOrder || store.contractStatus === 'signed'"
              @click.stop="handleOrder(store)"
            >
              订气/租赁
            </button>
          </view>
        </view>
      </view>
      
      <!-- 添加租户卡片 -->
      <view v-if="storeList.length > 0" class="store-card add-store-card" @click="handleRegisterStore">
        <view class="add-store-content">
          <view class="add-icon">+</view>
          <view class="add-text">添加新租户</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      selectedStoreId: null,
      storeList: [
        // 如果要测试无租户状态，可以注释掉下面的数组内容
        {
          id: 1,
          name: '城东燃气服务站',
          address: '东城区建国路88号',
          image: 'https://ts3.tc.mm.bing.net/th/id/OIP-C.P-mROslgWTHtZEoWdbNzCQHaFj?rs=1&pid=ImgDetMain&o=7&rm=3',
          isRecommended: true,
          contractStatus: 'signed',
          insuranceStatus: 'passed',
          safetyStatus: 'passed',
          safetyGuidanceStatus: 'passed', // 'not_scheduled', 'scheduled', 'passed', 'failed'
          canOrder: true,
          cylinderCount: 12,
          alarmCount: 3,
          hasAbnormal: false,
          latitude: 39.914889,
          longitude: 116.433832
        },
        {
          id: 2,
          name: '城西燃气服务中心',
          address: '西城区复兴门外大街15号',
          image: 'https://ts3.tc.mm.bing.net/th/id/OIP-C.P-mROslgWTHtZEoWdbNzCQHaFj?rs=1&pid=ImgDetMain&o=7&rm=3',
          isRecommended: false,
          contractStatus: 'unsigned',
          insuranceStatus: 'pending',
          safetyStatus: 'pending',
          safetyGuidanceStatus: 'not_scheduled', // 未预约，显示"前往预约"
          canOrder: false,
          cylinderCount: 8,
          alarmCount: 2,
          hasAbnormal: true,
          latitude: 39.908860,
          longitude: 116.352753
        },
        {
          id: 3,
          name: '南区燃气便民点',
          address: '丰台区丰台路20号',
          image: 'https://ts3.tc.mm.bing.net/th/id/OIP-C.P-mROslgWTHtZEoWdbNzCQHaFj?rs=1&pid=ImgDetMain&o=7&rm=3',
          isRecommended: false,
          contractStatus: 'unsigned',
          insuranceStatus: 'pending',
          safetyStatus: 'pending',
          safetyGuidanceStatus: 'passed', // 工单已结束，需要签署合同
          canOrder: false,
          cylinderCount: 15,
          alarmCount: 1,
          hasAbnormal: false,
          latitude: 39.858611,
          longitude: 116.287222
        },
        {
          id: 4,
          name: '北区燃气服务站',
          address: '朝阳区朝阳路100号',
          image: 'https://ts3.tc.mm.bing.net/th/id/OIP-C.P-mROslgWTHtZEoWdbNzCQHaFj?rs=1&pid=ImgDetMain&o=7&rm=3',
          isRecommended: false,
          contractStatus: 'signed',
          insuranceStatus: 'pending',
          safetyStatus: 'pending',
          safetyGuidanceStatus: 'scheduled', // 已预约，显示"查看预约"
          canOrder: false,
          cylinderCount: 20,
          alarmCount: 0,
          hasAbnormal: false,
          latitude: 39.928889,
          longitude: 116.435556
        },
        {
          id: 3,
          name: '南区燃气便民点',
          address: '丰台区丰台路20号',
          image: 'https://ts3.tc.mm.bing.net/th/id/OIP-C.P-mROslgWTHtZEoWdbNzCQHaFj?rs=1&pid=ImgDetMain&o=7&rm=3',
          isRecommended: false,
          contractStatus: 'unsigned',
          insuranceStatus: 'pending',
          safetyStatus: 'pending',
          safetyGuidanceStatus: 'scheduled',
          canOrder: false,
          cylinderCount: 5,
          alarmCount: 1,
          hasAbnormal: false,
          latitude: 39.858427,
          longitude: 116.286503
        },
        {
          id: 4,
          name: '北区燃气综合服务',
          address: '海淀区中关村大街1号',
          image: '/static/images/store4.png',
          isRecommended: false,
          contractStatus: 'signed',
          insuranceStatus: 'passed',
          safetyStatus: 'pending',
          safetyGuidanceStatus: 'failed',
          canOrder: false,
          cylinderCount: 15,
          alarmCount: 4,
          hasAbnormal: true,
          latitude: 39.983643,
          longitude: 116.318126
        }
      ]
    }
  },
  
  onLoad() {
    this.loadStores()
  },
  
  methods: {
    // 加载租户数据
    loadStores() {
      // 实际应用中这里应该调用API获取租户列表及其审核状态
      // 这里使用模拟数据
      uni.showLoading({
        title: '加载中'
      })
      
      // 模拟API请求延迟
      setTimeout(() => {
        // 如果需要测试空状态，可以在这里设置 this.storeList = []
        
        // 检查每个租户的审核状态，确定是否可以订气
        this.storeList.forEach(store => {
          store.canOrder = store.contractStatus === 'passed' && 
                         store.insuranceStatus === 'passed' && 
                         store.safetyStatus === 'passed'
        })
        
        uni.hideLoading()
      }, 1000)
    },
    
    // 选择租户
    selectStore(store) {
      this.selectedStoreId = store.id
    },

    // 注册新租户
    handleRegisterStore() {
      uni.navigateTo({
        url: "/pages/store/add-store"
      })
    },
    
    // 点击订气按钮
    handleOrder(store) {
      uni.navigateTo({
        url: `/pages/order/gas-order?id=${store.id}`
      })
    },
    
    // 点击前去审核按钮
    handleAudit(store) {
      uni.navigateTo({
        url: `/pages/store/index?id=${store.id}`
      })
    },

    // 查看租户详情
    viewStoreDetail(store) {
      uni.navigateTo({
        url: `/pages/store/detail?id=${store.id}`
      })
    },

    // 编辑租户
    handleEditStore(store) {
      uni.navigateTo({
        url: `/pages/store/add-store?id=${store.id}&edit=true`
      })
    },
    
    // 显示全屏地图
    showFullScreenMap(store) {
      uni.openLocation({
        latitude: store.latitude,
        longitude: store.longitude,
        name: store.name,
        address: store.address,
        scale: 16
      })
    },
    
    // 安全指导相关方法
    getSafetyGuidanceStatusText(status) {
      switch(status) {
        case 'not_scheduled': return '未预约';
        case 'scheduled': return '已预约';
        case 'passed': return '已通过';
        case 'failed': return '未通过';
        default: return '未预约';
      }
    },

    getSafetyGuidanceIcon(status) {
      switch(status) {
        case 'passed': return 'checkbox-filled';
        case 'failed': return 'closeempty';
        case 'scheduled': return 'calendar';
        default: return 'info';
      }
    },

    getSafetyGuidanceColor(status) {
      switch(status) {
        case 'passed': return '#19be6b';
        case 'failed': return '#fa3534';
        case 'scheduled': return '#ff9f43';
        default: return '#909399';
      }
    },

    getSafetyGuidanceTagClass(status) {
      switch(status) {
        case 'passed': return 'tag-passed';
        case 'failed': return 'tag-failed';
        case 'scheduled': return 'tag-scheduled';
        default: return 'tag-pending';
      }
    },
    
    // 判断是否已填入基本信息
    hasBasicInfo(store) {
      // 假设租户有基本信息就表示已填入基本信息
      // 实际项目中可以根据具体字段判断，比如 store.hasBasicInfo 或其他标识
      return store.name && store.address
    },

    // 判断工单是否已结束
    isWorkOrderCompleted(store) {
      // 假设当安全指导状态为 'passed' 时工单结束
      // 实际项目中可以根据具体的工单状态字段判断
      return store.safetyGuidanceStatus === 'passed'
    },

    // 判断是否已有预约
    hasAppointment(store) {
      // 假设当安全指导状态为 'scheduled' 时表示已预约
      // 实际项目中可以根据具体的预约状态字段判断
      return store.safetyGuidanceStatus === 'scheduled'
    },

    // 前往预约
    handleGoToAppointment(store) {
      uni.navigateTo({
        url: `/pages/store/add-store?id=${store.id}&step=1`
      })
    },

    // 查看预约
    handleViewAppointment(store) {
      uni.navigateTo({
        url: `/pages/store/add-store?id=${store.id}&step=1`
      })
    },

    // 签署合同
    handleSignContract(store) {
      uni.navigateTo({
        url: `/pages/store/add-store?id=${store.id}&step=2`
      })
    }
  }
}
</script>

<style scoped>
page {
  height: 100%;
}
.container {
  padding: 30rpx;
  min-height: 100%;
  background-color: #f8f8f8;
  position: relative;
  padding-bottom: 40rpx;
}

/* 顶部背景 */
.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 360rpx;
  background: linear-gradient(135deg, #3b5bdb 0%, #4c6ef5 100%);
  border-bottom-left-radius: 40rpx;
  border-bottom-right-radius: 40rpx;
  z-index: 0;
  /* background-image: url('/static/images/index-bg2.jpg'); */
  background-size: cover;
  background-position: center;
}

/* 欢迎区域 */
.welcome-section {
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 60rpx 40rpx 30rpx;
}

.welcome-text {
  display: flex;
  flex-direction: column;
}

.greeting {
  color: #fff;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.sub-greeting {
  color: rgba(255, 255, 255, 0.9);
  font-size: 28rpx;
}

.user-avatar {
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  overflow: hidden;
  border: 4rpx solid rgba(255, 255, 255, 0.6);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}

.user-avatar image {
  width: 100%;
  height: 100%;
}

/* 租户列表容器 */
.stores-container {
  position: relative;
  z-index: 1;
  padding-bottom: 60rpx;
}

/* 无租户时的缺省状态 */
.empty-store-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
  margin: 60rpx 0;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.empty-store-image {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 40rpx;
}

.empty-store-text {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-store-subtext {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 50rpx;
}

.register-btn {
  width: 70%;
  height: 90rpx;
  line-height: 90rpx;
  font-size: 32rpx;
  padding: 0;
  background: linear-gradient(135deg, #4c6ef5 0%, #3b5bdb 100%);
  color: #fff;
  border-radius: 45rpx;
  box-shadow: 0 10rpx 20rpx rgba(75, 110, 245, 0.3);
}

/* 租户卡片样式 */
.store-card {
  background-color: #fff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  border: 2rpx solid transparent;
  transition: all 0.3s;
}

.store-active {
  border-color: #4c6ef5;
  /* background-color: rgba(76, 110, 245, 0.05); */
  box-shadow: 0 6rpx 20rpx rgba(76, 110, 245, 0.15);
}

/* 租户头部信息 */
.store-header {
  display: flex;
  margin-bottom: 30rpx;
}

.store-image {
  width: 150rpx;
  height: 150rpx;
  border-radius: 15rpx;
  margin-right: 20rpx;
}

.store-info {
  flex: 1;
  position: relative;
}

.store-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  cursor: pointer;
  color: var(--primary-color);
}

.store-address {
  font-size: 26rpx;
  color: #999;
}

.store-tag {
  position: absolute;
  top: 0;
  right: 0;
  background-color: rgba(255, 100, 0, 0.85);
  color: white;
  font-size: 22rpx;
  padding: 6rpx 14rpx;
  border-radius: 10rpx;
}

/* 审核状态样式 */
.store-status {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
  background-color: #f9f9f9;
  padding: 20rpx;
  border-radius: 16rpx;
}

.status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 30%;
  transition: all 0.3s ease;
}

.item-icon {
  margin-bottom: 12rpx;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s;
}

.icon-passed {
  background-color: rgba(25, 190, 107, 0.1);
}

.icon-operational {
  background-color: rgba(76, 110, 245, 0.1);
}

.icon-normal {
  background-color: rgba(25, 190, 107, 0.1);
}

.icon-abnormal {
  background-color: rgba(250, 53, 52, 0.1);
}

.item-text {
  font-size: 24rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.status-tag {
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.tag-passed {
  background-color: rgba(25, 190, 107, 0.1);
  color: #19be6b;
}

.tag-pending {
  background-color: rgba(250, 53, 52, 0.1);
  color: #fa3534;
}

.tag-normal {
  background-color: rgba(25, 190, 107, 0.1);
  color: #19be6b;
}

.tag-abnormal {
  background-color: rgba(250, 53, 52, 0.1);
  color: #fa3534;
}

.operational-item {
  padding: 10rpx 0;
}

.operational-value {
  font-size: 24rpx;
  font-weight: bold;
  color: #4c6ef5;
}

/* 租户操作区域 */
.store-action {
  display: flex;
  justify-content: center;
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.order-btn, .audit-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 32rpx;
  margin: 0;
}

.order-btn {
  width: 48%;
}

.edit-btn {
  width: 48%;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 32rpx;
  margin: 0;
  background-color: #f0f0f0;
  color: #333;
  border-radius: 40rpx;
  border: none;
}
.edit-btn:after {
  border: none;
}

.detail-btn {
  display: none;
}

.order-btn {
  padding: 0;
  background: linear-gradient(135deg, #4c6ef5 0%, #3b5bdb 100%);
  color: #fff;
  border-radius: 40rpx;
  box-shadow: 0 8rpx 16rpx rgba(75, 110, 245, 0.3);
}

.audit-btn {
  padding: 0;
  background: linear-gradient(135deg, #ff9f43 0%, #ff7f00 100%);
  color: #fff;
  border-radius: 40rpx;
  box-shadow: 0 8rpx 16rpx rgba(255, 159, 67, 0.3);
}

.store-map {
  margin-top: 15rpx;
  width: 110rpx;
  height: 110rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #f0f0f0;
  position: relative;
  cursor: pointer;
}

.store-map .map {
  width: 100%;
  height: 180rpx;
}

/* 添加一个小图标表示可点击 */
.store-map:after {
  content: '';
  position: absolute;
  bottom: 6rpx;
  right: 6rpx;
  width: 24rpx;
  height: 24rpx;
  background-color: rgba(76, 110, 245, 0.8);
  border-radius: 50%;
  z-index: 5;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M15,3l2.3,2.3l-2.89,2.87l1.42,1.42L18.7,6.7L21,9V3H15z M3,9l2.3-2.3l2.87,2.89l1.42-1.42L6.7,5.3L9,3H3V9z M9,21 l-2.3-2.3l2.89-2.87l-1.42-1.42L5.3,17.3L3,15v6H9z M21,15l-2.3,2.3l-2.87-2.89l-1.42,1.42l2.89,2.87L15,21h6V15z'/%3E%3C/svg%3E");
  background-size: 16rpx 16rpx;
  background-position: center;
  background-repeat: no-repeat;
}

/* 新增的审核状态行样式 */
.audit-status-row {
  display: flex;
  justify-content: space-around;
  margin: 20rpx 0;
  padding: 10rpx 0;
  background-color: #f0f0f0;
  border-radius: 10rpx;
}

.audit-item {
  display: flex;
  align-items: center;
  padding: 0 10rpx;
}

.audit-icon {
  margin-right: 8rpx;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: #e0e0e0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.icon-passed {
  background-color: rgba(25, 190, 107, 0.2);
}

.audit-text {
  font-size: 24rpx;
  color: #666;
}

/* 添加租户卡片样式 */
.add-store-card {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  /* border: 2rpx dashed #4c6ef5; */
  cursor: pointer;
  padding: 40rpx 0;
  opacity: 0.9;
  transition: all 0.3s;
}

.add-store-card:hover, .add-store-card:active {
  background-color: rgba(76, 110, 245, 0.05);
  opacity: 1;
}

.add-store-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.add-icon {
  font-size: 60rpx;
  color: #4c6ef5;
  margin-bottom: 10rpx;
  line-height: 1;
}

.add-text {
  font-size: 28rpx;
  color: #4c6ef5;
  font-weight: 500;
}

.btn-warning {
  margin-right: 4% !important;
  padding: 0;
  background: linear-gradient(135deg, #ff9f43 0%, #ff7f00 100%);
  color: #fff;
  border-radius: 40rpx;
  box-shadow: 0 8rpx 16rpx rgba(255, 159, 67, 0.3);
}

.schedule-btn {
  width: 48%;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 32rpx;
  margin: 0;
}

.btn-info {
  margin-right: 4% !important;
  padding: 0;
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
  color: #fff;
  border-radius: 40rpx;
  box-shadow: 0 8rpx 16rpx rgba(23, 162, 184, 0.3);
}

.contract-btn {
  width: 48%;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 32rpx;
  margin: 0;
}

/* 安全指导状态相关样式 */
.status-failed .item-icon {
  background-color: rgba(250, 53, 52, 0.1);
}

.status-scheduled .item-icon {
  background-color: rgba(255, 159, 67, 0.1);
}

.icon-failed {
  background-color: rgba(250, 53, 52, 0.1);
}

.icon-scheduled {
  background-color: rgba(255, 159, 67, 0.1);
}

.tag-failed {
  background-color: rgba(250, 53, 52, 0.1);
  color: #fa3534;
}

.tag-scheduled {
  background-color: rgba(255, 159, 67, 0.1);
  color: #ff9f43;
}
</style> 