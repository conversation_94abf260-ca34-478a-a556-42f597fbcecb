<template>
  <view class="container">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 主要内容 -->
    <view v-else class="content">
      <!-- 服务商信息 -->
      <view class="section">
        <view class="section-title">
          <view class="section-title-bar"></view>
          <text>服务商信息</text>
        </view>
        <view class="info-card">
          <view class="info-row">
            <text class="info-label">服务商名称</text>
            <text class="info-value">{{ safetyCheckData.serviceProvider.name }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">联系人</text>
            <text class="info-value">{{ safetyCheckData.serviceProvider.contact }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">联系电话</text>
            <text class="info-value">{{ safetyCheckData.serviceProvider.phone }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">核查时间</text>
            <text class="info-value">{{ safetyCheckData.serviceProvider.checkTime }}</text>
          </view>
        </view>
      </view>

      <!-- 使用场景 -->
      <view class="section">
        <view class="section-title">
          <view class="section-title-bar"></view>
          <text>使用场景</text>
        </view>
        <view class="image-section">
          <text class="scene-desc">{{ safetyCheckData.usageScene.description }}</text>
          <view class="image-grid">
            <view 
              v-for="(image, index) in safetyCheckData.usageScene.images" 
              :key="index"
              class="image-item"
              @click="previewImage(image, safetyCheckData.usageScene.images)"
            >
              <image :src="image" mode="aspectFill" class="scene-image"></image>
            </view>
          </view>
        </view>
      </view>

      <!-- 气瓶存放区 -->
      <view class="section">
        <view class="section-title">
          <view class="section-title-bar"></view>
          <text>气瓶存放区</text>
        </view>
        <view class="image-section">
          <view class="image-grid">
            <view 
              v-for="(image, index) in safetyCheckData.cylinderStorage.images" 
              :key="index"
              class="image-item"
              @click="previewImage(image, safetyCheckData.cylinderStorage.images)"
            >
              <image :src="image" mode="aspectFill" class="scene-image"></image>
            </view>
          </view>
        </view>
      </view>

      <!-- 报警器安装 -->
      <view class="section">
        <view class="section-title">
          <view class="section-title-bar"></view>
          <text>报警器安装</text>
        </view>
        <view class="image-section">
          <view class="image-grid">
            <view 
              v-for="(image, index) in safetyCheckData.alarmInstallation.images" 
              :key="index"
              class="image-item"
              @click="previewImage(image, safetyCheckData.alarmInstallation.images)"
            >
              <image :src="image" mode="aspectFill" class="scene-image"></image>
            </view>
          </view>
        </view>
      </view>

      <!-- 消防设备 -->
      <view class="section">
        <view class="section-title">
          <view class="section-title-bar"></view>
          <text>消防设备</text>
        </view>
        <view class="image-section">
          <view class="image-grid">
            <view 
              v-for="(image, index) in safetyCheckData.fireEquipment.images" 
              :key="index"
              class="image-item"
              @click="previewImage(image, safetyCheckData.fireEquipment.images)"
            >
              <image :src="image" mode="aspectFill" class="scene-image"></image>
            </view>
          </view>
        </view>
      </view>

      <!-- 管道阀门安装图 -->
      <view class="section">
        <view class="section-title">
          <view class="section-title-bar"></view>
          <text>管道阀门安装图</text>
        </view>
        <view class="image-section">
          <view class="image-grid">
            <view 
              v-for="(image, index) in safetyCheckData.pipelineValve.images" 
              :key="index"
              class="image-item"
              @click="previewImage(image, safetyCheckData.pipelineValve.images)"
            >
              <image :src="image" mode="aspectFill" class="scene-image"></image>
            </view>
          </view>
        </view>
      </view>

      <!-- 燃气设备使用图 -->
      <view class="section">
        <view class="section-title">
          <view class="section-title-bar"></view>
          <text>燃气设备使用图</text>
        </view>
        <view class="image-section">
          <text class="scene-desc">{{ safetyCheckData.gasEquipment.description }}</text>
          <view class="image-grid">
            <view 
              v-for="(image, index) in safetyCheckData.gasEquipment.images" 
              :key="index"
              class="image-item"
              @click="previewImage(image, safetyCheckData.gasEquipment.images)"
            >
              <image :src="image" mode="aspectFill" class="scene-image"></image>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'

// 响应式数据
const storeId = ref(null)
const loading = ref(true)
const safetyCheckData = ref({})

// 页面加载时的处理
const onLoad = (options) => {
  storeId.value = options.id;
  loadSafetyCheckData();
}

// 加载安全核查数据
const loadSafetyCheckData = () => {
  loading.value = true;

  // 模拟API调用
  setTimeout(() => {
    safetyCheckData.value = {
      serviceProvider: {
        name: "广州市燃气安全检测有限公司",
        contact: "李工程师",
        phone: "020-12345678",
        checkTime: "2023-10-15 14:30"
      },
      usageScene: {
        description: "通风良好，有通风设备",
        images: [
          "https://via.placeholder.com/300x200/4a66b7/ffffff?text=通风设备1",
          "https://via.placeholder.com/300x200/2979ff/ffffff?text=通风设备2"
        ]
      },
      cylinderStorage: {
        images: [
          "https://via.placeholder.com/300x200/34c759/ffffff?text=气瓶存放1",
          "https://via.placeholder.com/300x200/ff9500/ffffff?text=气瓶存放2",
          "https://via.placeholder.com/300x200/ff3b30/ffffff?text=气瓶存放3"
        ]
      },
      alarmInstallation: {
        images: [
          "https://via.placeholder.com/300x200/909399/ffffff?text=报警器1",
          "https://via.placeholder.com/300x200/606266/ffffff?text=报警器2"
        ]
      },
      fireEquipment: {
        images: [
          "https://via.placeholder.com/300x200/e6a23c/ffffff?text=消防设备1",
          "https://via.placeholder.com/300x200/f56c6c/ffffff?text=消防设备2",
          "https://via.placeholder.com/300x200/67c23a/ffffff?text=消防设备3"
        ]
      },
      pipelineValve: {
        images: [
          "https://via.placeholder.com/300x200/409eff/ffffff?text=管道阀门1",
          "https://via.placeholder.com/300x200/303133/ffffff?text=管道阀门2"
        ]
      },
      gasEquipment: {
        description: "灶具有息火保护装置，热水器烟道是强排",
        images: [
          "https://via.placeholder.com/300x200/909399/ffffff?text=燃气灶具",
          "https://via.placeholder.com/300x200/606266/ffffff?text=热水器",
          "https://via.placeholder.com/300x200/303133/ffffff?text=烟道设备"
        ]
      }
    };

    loading.value = false;
  }, 1000);
}

// 预览图片
const previewImage = (currentImage, imageList) => {
  uni.previewImage({
    urls: imageList,
    current: currentImage
  });
}

// 直接加载数据
loadSafetyCheckData();

// 导出页面生命周期方法供uni-app使用
defineExpose({
  onLoad
})
</script>

<style scoped>
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid rgba(74, 102, 183, 0.1);
  border-top: 6rpx solid #4a66b7;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 30rpx;
  color: #4a66b7;
  font-size: 32rpx;
  font-weight: 500;
}

/* 内容区域 */
.content {
  padding: 40rpx 24rpx 60rpx;
  max-width: 750rpx;
  margin: 0 auto;
}

.section {
  margin-bottom: 48rpx;
  position: relative;
}

.section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  padding: 0 8rpx;
  position: relative;
}

.section-title-bar {
  width: 8rpx;
  height: 36rpx;
  background: linear-gradient(135deg, #4a66b7, #667eea);
  margin-right: 20rpx;
  border-radius: 4rpx;
  box-shadow: 0 2rpx 8rpx rgba(74, 102, 183, 0.3);
}

.section-title text {
  font-size: 36rpx;
  font-weight: 700;
  color: #2c3e50;
  letter-spacing: 1rpx;
}

/* 信息卡片 */
.info-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 24rpx;
  padding: 48rpx 36rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10rpx);
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.info-row:hover {
  background-color: rgba(74, 102, 183, 0.02);
  border-radius: 12rpx;
  margin: 0 -12rpx;
  padding: 24rpx 12rpx;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  width: 240rpx;
  font-size: 28rpx;
  color: #5a6c7d;
  font-weight: 500;
  flex-shrink: 0;
}

.info-value {
  flex: 1;
  text-align: right;
  font-size: 30rpx;
  color: #2c3e50;
  font-weight: 600;
  word-break: break-all;
}

/* 图片区域 */
.image-section {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 24rpx;
  padding: 48rpx 36rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10rpx);
}

.scene-desc {
  display: block;
  font-size: 30rpx;
  color: #5a6c7d;
  margin-bottom: 36rpx;
  line-height: 1.7;
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  padding: 28rpx 24rpx;
  border-radius: 16rpx;
  border-left: 6rpx solid #4a66b7;
  font-weight: 500;
  box-shadow: 0 4rpx 16rpx rgba(74, 102, 183, 0.1);
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

.image-item {
  height: 260rpx;
  border-radius: 20rpx;
  overflow: hidden;
  position: relative;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.image-item:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}

.image-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(74, 102, 183, 0.1) 0%, rgba(102, 126, 234, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.image-item:active::before {
  opacity: 1;
}

.scene-image {
  width: 100%;
  height: 100%;
  border-radius: 20rpx;
  object-fit: cover;
}

/* 响应式布局 */
@media (max-width: 600rpx) {
  .content {
    padding: 32rpx 20rpx 48rpx;
  }

  .section {
    margin-bottom: 40rpx;
  }

  .info-card, .image-section {
    padding: 36rpx 28rpx;
  }

  .image-grid {
    gap: 20rpx;
  }

  .image-item {
    height: 240rpx;
  }
}
</style>
